# 导诊单模块

## 功能概述

导诊单模块提供了完整的导诊单查看功能，包括：

- 导诊单列表查看
- 导诊单详情展示
- 条形码/二维码显示
- 患者信息管理

## 文件结构

```
src/pages/guide-sheet/
├── list/                           # 导诊单列表页面
│   ├── index.vue                   # 列表页面主组件
│   ├── hooks.ts                    # 列表页面业务逻辑
│   └── components/                 # 列表页面组件
│       ├── patient-card-display.vue   # 患者卡显示组件
│       └── guide-sheet-item.vue        # 导诊单列表项组件
├── detail/                         # 导诊单详情页面
│   ├── index.vue                   # 详情页面主组件
│   ├── hooks.ts                    # 详情页面业务逻辑
│   └── components/                 # 详情页面组件
│       ├── barcode-canvas.vue          # 条形码组件
│       └── qrcode-canvas.vue           # 二维码组件
├── services/                       # API服务
│   └── api.ts                      # 导诊单相关API
├── types/                          # 类型定义
│   └── index.ts                    # 导诊单相关类型
└── README.md                       # 说明文档
```

## 页面路由

### 导诊单列表页面
- 路径: `/pages/guide-sheet/list/index`
- 参数:
  - `cardId` (可选): 患者卡ID
  - `pmiNo` (可选): 患者登记号

### 导诊单详情页面
- 路径: `/pages/guide-sheet/detail/index`
- 参数:
  - `cardId` (必需): 患者卡ID
  - `guideInfoId` (必需): 导诊单系统ID
  - `pmiNo` (可选): 患者登记号

## API接口

### 获取导诊单列表
```typescript
getGuideSheetList(params: GuideSheetListParams): Promise<GuideSheetListResponse>
```

### 获取导诊单详情
```typescript
getGuideSheetDetail(params: GuideSheetDetailParams): Promise<GuideSheetDetailResponse>
```

## 使用方式

### 1. 从首页进入
首页菜单中的"导诊单"项会自动跳转到导诊单列表页面。

### 2. 直接跳转
```typescript
// 跳转到导诊单列表
uni.navigateTo({
  url: '/pages/guide-sheet/list/index'
});

// 跳转到指定患者的导诊单列表
uni.navigateTo({
  url: `/pages/guide-sheet/list/index?cardId=${cardId}&pmiNo=${pmiNo}`
});

// 跳转到导诊单详情
uni.navigateTo({
  url: `/pages/guide-sheet/detail/index?cardId=${cardId}&guideInfoId=${guideInfoId}`
});
```

## 功能特性

### 导诊单列表
- 支持患者切换
- 支持分页加载
- 支持下拉刷新
- 空状态处理

### 导诊单详情
- 患者信息展示
- 导诊信息展示
- 条形码/二维码显示
- 预约按钮（可配置）

### 条形码/二维码显示规则
- 当 `displayQrCode` 不为 1 或 2 时，显示条形码
- 当 `displayQrCode` 为 1 或 2 时，显示二维码
- 优先显示 `qrCode` 字段的值，如果为空则使用 `registerNo`

## 注意事项

1. 导诊单功能依赖患者卡模块，确保患者卡功能正常
2. 条形码和二维码组件使用Canvas绘制，实际项目中建议使用专业的条形码/二维码生成库
3. 分页组件使用z-paging，确保已正确安装和配置
4. API接口路径需要根据实际后端接口进行调整

## 兼容性

- 支持微信小程序
- 支持支付宝小程序
- 支持抖音小程序
- 支持H5
- 支持App
