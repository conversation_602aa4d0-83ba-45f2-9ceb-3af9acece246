<template>
  <view class="guide-sheet-item" @click="handleClick">
    <view class="item-header">
      <view class="item-title">
        <text class="title-text">{{ item.patientName || '门诊导诊单' }}</text>
        <text class="title-badge">门诊</text>
      </view>
      <view class="item-time">{{ formatTime(item.admTime) }}</view>
    </view>

    <view class="item-content">
      <view class="info-row">
        <view class="info-label">就诊人：</view>
        <view class="info-value">{{ item.patientName }}</view>
      </view>
      <view class="info-row" v-if="item.gender">
        <view class="info-label">性别：</view>
        <view class="info-value">{{ item.gender }}</view>
      </view>
      <view class="info-row" v-if="item.age">
        <view class="info-label">年龄：</view>
        <view class="info-value">{{ item.age }}岁</view>
      </view>
      <view class="info-row" v-if="item.registerNo">
        <view class="info-label">登记号：</view>
        <view class="info-value">{{ item.registerNo }}</view>
      </view>
      <view class="info-row" v-if="item.hospitalName">
        <view class="info-label">医院：</view>
        <view class="info-value">{{ item.hospitalName }}</view>
      </view>
    </view>

    <view class="item-footer">
      <view class="footer-left">
        <text class="view-detail">查看详情</text>
      </view>
      <view class="footer-right">
        <text class="arrow-icon">></text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import type { GuideSheetContent } from "../../types";

interface Props {
  item: GuideSheetContent;
}

interface Emits {
  (e: "click", item: GuideSheetContent): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

/**
 * 格式化时间
 */
const formatTime = (timeStr?: string) => {
  if (!timeStr) return "";
  
  try {
    const date = new Date(timeStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  } catch (error) {
    return timeStr;
  }
};

/**
 * 处理点击事件
 */
const handleClick = () => {
  emit("click", props.item);
};
</script>

<style lang="scss" scoped>
@use "../../../../styles/tokens/colors.scss" as colors;

.guide-sheet-item {
  background-color: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
  }

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16rpx;
    padding-bottom: 16rpx;
    border-bottom: 1rpx solid #e5e5e5;

    .item-title {
      display: flex;
      align-items: center;
      gap: 12rpx;

      .title-text {
        font-size: 32rpx;
        font-weight: 600;
        color: colors.$text-primary;
      }

      .title-badge {
        background-color: colors.$color-primary;
        color: white;
        font-size: 20rpx;
        padding: 4rpx 8rpx;
        border-radius: 4rpx;
      }
    }

    .item-time {
      font-size: 24rpx;
      color: colors.$text-secondary;
    }
  }

  .item-content {
    margin-bottom: 16rpx;

    .info-row {
      display: flex;
      margin-bottom: 8rpx;
      font-size: 28rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        color: colors.$text-secondary;
        min-width: 120rpx;
      }

      .info-value {
        color: colors.$text-primary;
        flex: 1;
      }
    }
  }

  .item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 16rpx;
    border-top: 1rpx solid #f0f0f0;

    .footer-left {
      .view-detail {
        font-size: 26rpx;
        color: colors.$color-primary;
      }
    }

    .footer-right {
      .arrow-icon {
        font-size: 24rpx;
        color: colors.$text-secondary;
      }
    }
  }
}
</style>
