<template>
  <hyt-page navbarTitle="导诊单" :loading="loading">
    <view class="guide-sheet-container">
      <!-- 患者卡片区域 -->
      <view class="header" v-if="patientInfo">
        <PatientCardDisplay :patientInfo="patientInfo" @change="handlePatientChange" />
      </view>

      <!-- 导诊单列表 -->
      <view class="guide-sheet-list" v-if="!loading && guideSheetList.length > 0">
        <z-paging
          ref="paging"
          v-model="guideSheetList"
          @query="queryList"
          :fixed="false"
          :use-page-scroll="true"
          :auto="false"
        >
          <GuideSheetItem
            v-for="item in guideSheetList"
            :key="item.guideInfoId"
            :item="item"
            @click="handleItemClick(item)"
          />
        </z-paging>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && guideSheetList.length === 0 && patientInfo">
        <view class="empty-icon">📋</view>
        <view class="empty-text">暂无导诊单</view>
      </view>

      <!-- 无患者卡状态 -->
      <view class="no-patient-state" v-if="!loading && !patientInfo">
        <view class="empty-icon">👤</view>
        <view class="empty-text">请先选择就诊人</view>
        <view class="empty-action" @click="selectPatient">选择就诊人</view>
      </view>
    </view>
  </hyt-page>
</template>

<script setup lang="ts">
import { useGuideSheetList } from "./hooks";
import PatientCardDisplay from "./components/patient-card-display.vue";
import GuideSheetItem from "./components/guide-sheet-item.vue";

// 使用导诊单列表 Hook
const {
  // 响应式数据
  guideSheetList,
  patientInfo,
  loading,
  paging,

  // 方法
  queryList,
  handleItemClick,
  handlePatientChange,
  selectPatient,
} = useGuideSheetList();
</script>

<style lang="scss" scoped>
@use "@/styles/tokens/colors.scss" as colors;

.guide-sheet-container {
  min-height: 100vh;
  background-color: colors.$color-background;
  padding: 21rpx 30rpx;
}

.header {
  margin-bottom: 24rpx;
}

.guide-sheet-list {
  flex: 1;
}

.empty-state,
.no-patient-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;

  .empty-icon {
    font-size: 80rpx;
    margin-bottom: 24rpx;
    opacity: 0.6;
  }

  .empty-text {
    font-size: 28rpx;
    color: colors.$text-secondary;
    margin-bottom: 24rpx;
  }

  .empty-action {
    padding: 16rpx 32rpx;
    background-color: colors.$color-primary;
    color: white;
    border-radius: 8rpx;
    font-size: 28rpx;
  }
}
</style>
