/**
 * 导诊单相关类型定义
 */

/**
 * 导诊单内容信息
 */
export interface GuideSheetContent {
  /** 开单id/处方id/就诊id */
  admId?: string;
  /** 开单时间 */
  admTime?: string;
  /** 年龄 */
  age?: string;
  /** 患者卡号 */
  cardNo?: string;
  /** 性别 */
  gender?: string;
  /** 导诊信息 */
  guideData?: string;
  /** 导诊单系统id */
  guideInfoId?: string;
  /** 导诊单字段 默认（门诊） */
  guideType?: string;
  /** 机构编码 */
  hospitalCode?: string;
  /** 机构名称 */
  hospitalName?: string;
  /** 门诊缴费订单号 */
  outPatientId?: string;
  /** 患者姓名 */
  patientName?: string;
  /** 患者登记号 */
  registerNo?: string;
  /** 天府医院是否存在华西外检 */
  jumpFlag?: boolean;
  /** 登记号是否展示二维码 1展示 0不展示，默认不展示二维码(展示条形码) */
  displayQrCode?: number | string;
  /** 需要展示二维码的值，为空不展示 */
  qrCode?: number | string;
  /** 卡类型 */
  cardTypeCode?: string;
  /** 是否显示预约按钮 */
  showAppointmentBtn?: boolean;
}

/**
 * 导诊单列表查询参数
 */
export interface GuideSheetListParams {
  /** 患者卡ID */
  cardId: string;
  /** 患者登记号 */
  pAPMI: string;
  /** 页码 */
  pageNum: number;
  /** 每页数量 */
  pageSize: number;
}

/**
 * 导诊单列表响应数据
 */
export interface GuideSheetListResponse {
  /** 是否升序 */
  asc?: boolean;
  /** 导诊单列表内容 */
  content?: GuideSheetContent[];
  /** 排序字段 */
  orderBy?: string;
  /** 当前页码 */
  pageNum?: number;
  /** 每页数量 */
  pageSize?: number;
  /** 总数量 */
  total?: number;
  /** 总页数 */
  totalPages?: number;
}

/**
 * 导诊单详情查询参数
 */
export interface GuideSheetDetailParams {
  /** 患者卡ID */
  cardId: string;
  /** 导诊单系统id */
  guideInfoId: string;
}

/**
 * 导诊单详情响应数据
 */
export interface GuideSheetDetailResponse extends GuideSheetContent {}

/**
 * 患者信息
 */
export interface PatientInfo {
  /** 患者卡ID */
  cardId: string;
  /** 患者姓名 */
  patientName: string;
  /** 患者登记号 */
  pmi: string;
  /** 性别 */
  gender?: string;
  /** 年龄 */
  age?: string;
  /** 卡号 */
  cardNo?: string;
  /** 卡类型 */
  cardTypeCode?: string;
}

/**
 * 导诊单列表页面状态
 */
export interface GuideSheetListState {
  /** 导诊单列表 */
  guideSheetList: GuideSheetContent[];
  /** 当前页码 */
  pageNum: number;
  /** 每页数量 */
  pageSize: number;
  /** 是否正在加载 */
  isLoading: boolean;
  /** 是否还有更多数据 */
  hasMore: boolean;
  /** 当前选中的患者信息 */
  patientInfo: PatientInfo | null;
}
