<template>
  <canvas 
    :canvas-id="canvasId" 
    class="barcode-canvas"
    :style="{ width: width + 'px', height: height + 'px' }"
  ></canvas>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";

interface Props {
  value: string;
  width?: number;
  height?: number;
}

const props = withDefaults(defineProps<Props>(), {
  width: 300,
  height: 80,
});

const canvasId = ref(`barcode-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);

/**
 * 绘制条形码
 */
const drawBarcode = () => {
  if (!props.value) return;

  const ctx = uni.createCanvasContext(canvasId.value);
  
  // 清空画布
  ctx.clearRect(0, 0, props.width, props.height);
  
  // 设置背景色
  ctx.setFillStyle('#ffffff');
  ctx.fillRect(0, 0, props.width, props.height);
  
  // 简单的条形码绘制逻辑（模拟）
  // 实际项目中应该使用专业的条形码生成库
  const barWidth = 2;
  const barHeight = props.height - 20;
  const startY = 10;
  let currentX = 10;
  
  // 设置条形码颜色
  ctx.setFillStyle('#000000');
  
  // 根据值生成条形码模式
  for (let i = 0; i < props.value.length; i++) {
    const char = props.value.charCodeAt(i);
    const pattern = char % 8; // 简单的模式生成
    
    for (let j = 0; j < 8; j++) {
      if ((pattern >> j) & 1) {
        ctx.fillRect(currentX, startY, barWidth, barHeight);
      }
      currentX += barWidth;
    }
    currentX += barWidth; // 字符间隔
  }
  
  // 绘制文本
  ctx.setFillStyle('#000000');
  ctx.setFontSize(12);
  ctx.setTextAlign('center');
  ctx.fillText(props.value, props.width / 2, props.height - 5);
  
  ctx.draw();
};

// 监听值变化
watch(() => props.value, () => {
  drawBarcode();
});

// 组件挂载后绘制
onMounted(() => {
  // 延迟绘制，确保canvas已经渲染
  setTimeout(() => {
    drawBarcode();
  }, 100);
});
</script>

<style lang="scss" scoped>
.barcode-canvas {
  border: 1rpx solid #e9ecef;
  background-color: white;
  border-radius: 4rpx;
}
</style>
