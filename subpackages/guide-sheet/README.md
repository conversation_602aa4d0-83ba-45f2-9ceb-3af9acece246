# 导诊单子包

## 概述

导诊单子包是一个独立的功能模块，提供完整的导诊单查看功能。作为子包，它可以按需加载，减少主包体积。

## 文件结构

```
subpackages/guide-sheet/
├── pages/                          # 页面文件
│   ├── list/                       # 导诊单列表页面
│   │   ├── index.vue               # 列表页面主组件
│   │   ├── hooks.ts                # 列表页面业务逻辑
│   │   └── components/             # 列表页面组件
│   │       ├── patient-card-display.vue   # 患者卡显示组件
│   │       └── guide-sheet-item.vue       # 导诊单列表项组件
│   └── detail/                     # 导诊单详情页面
│       ├── index.vue               # 详情页面主组件
│       ├── hooks.ts                # 详情页面业务逻辑
│       └── components/             # 详情页面组件
│           ├── barcode-canvas.vue      # 条形码组件
│           └── qrcode-canvas.vue       # 二维码组件
├── services/                       # API服务
│   └── api.ts                      # 导诊单相关API
├── types/                          # 类型定义
│   └── index.ts                    # 导诊单相关类型
└── README.md                       # 说明文档
```

## 页面路由

### 导诊单列表页面
- 路径: `/subpackages/guide-sheet/pages/list/index`
- 参数:
  - `cardId` (可选): 患者卡ID
  - `pmiNo` (可选): 患者登记号

### 导诊单详情页面
- 路径: `/subpackages/guide-sheet/pages/detail/index`
- 参数:
  - `cardId` (必需): 患者卡ID
  - `guideInfoId` (必需): 导诊单系统ID
  - `pmiNo` (可选): 患者登记号

## 子包配置

在 `pages.json` 中的配置：

```json
{
  "subPackages": [
    {
      "root": "subpackages/guide-sheet",
      "name": "guide-sheet",
      "pages": [
        {
          "path": "pages/list/index",
          "style": {
            "navigationBarTitleText": "导诊单",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "pages/detail/index",
          "style": {
            "navigationBarTitleText": "导诊单详情",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }
        }
      ]
    }
  ]
}
```

## API接口

### 获取导诊单列表
```typescript
getGuideSheetList(params: GuideSheetListParams): Promise<GuideSheetListResponse>
```

### 获取导诊单详情
```typescript
getGuideSheetDetail(params: GuideSheetDetailParams): Promise<GuideSheetDetailResponse>
```

## 使用方式

### 1. 从主包跳转
```typescript
// 跳转到导诊单列表
uni.navigateTo({
  url: '/subpackages/guide-sheet/pages/list/index'
});

// 跳转到指定患者的导诊单列表
uni.navigateTo({
  url: `/subpackages/guide-sheet/pages/list/index?cardId=${cardId}&pmiNo=${pmiNo}`
});

// 跳转到导诊单详情
uni.navigateTo({
  url: `/subpackages/guide-sheet/pages/detail/index?cardId=${cardId}&guideInfoId=${guideInfoId}`
});
```

### 2. 首页菜单集成
首页菜单中的"导诊单"项会自动跳转到导诊单列表页面，路径已在 `PageInteractionHandler` 中配置。

## 功能特性

### 导诊单列表
- ✅ 支持患者切换
- ✅ 支持分页加载
- ✅ 支持下拉刷新
- ✅ 空状态处理
- ✅ 响应式设计

### 导诊单详情
- ✅ 患者信息展示
- ✅ 导诊信息展示
- ✅ 条形码/二维码显示
- ✅ 预约按钮（可配置）

### 条形码/二维码显示规则
- 当 `displayQrCode` 不为 1 或 2 时，显示条形码
- 当 `displayQrCode` 为 1 或 2 时，显示二维码
- 优先显示 `qrCode` 字段的值，如果为空则使用 `registerNo`

## 子包优势

1. **按需加载** - 只有在用户访问导诊单功能时才会下载子包代码
2. **减少主包体积** - 主包体积更小，首次启动更快
3. **独立维护** - 导诊单功能可以独立开发和维护
4. **模块化** - 清晰的模块边界，便于团队协作

## 依赖关系

### 主包依赖
- `@/utils/request` - HTTP请求工具
- `@/pages/patient-card/shared/services/patientCardService` - 患者卡服务
- `@/pages/patient-card/shared/types` - 患者卡类型
- `@/styles/tokens/colors.scss` - 样式变量

### 第三方依赖
- `z-paging` - 分页组件
- `uni-app` - 框架API

## 注意事项

1. **路径引用** - 子包内部使用相对路径，引用主包资源使用绝对路径
2. **类型导入** - 确保类型定义的导入路径正确
3. **样式引用** - 使用 `@/` 别名引用主包样式文件
4. **API调用** - 使用主包的request工具，确保配置一致

## 兼容性

- ✅ 微信小程序
- ✅ 支付宝小程序
- ✅ 抖音小程序
- ✅ H5
- ✅ App

## 开发建议

1. **测试覆盖** - 为子包功能编写完整的测试用例
2. **性能优化** - 监控子包加载性能，优化资源大小
3. **错误处理** - 完善错误处理和用户提示
4. **文档维护** - 及时更新文档，保持与代码同步
